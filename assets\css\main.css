@tailwind base;
@tailwind components;
@tailwind utilities;

.group:hover .group-hover\:block {
  display: block;
}
.hover\:w-64:hover {
  width: 45%;
}
/* NO NEED THIS CSS - just for custom scrollbar which can also be configured in tailwind.config.js*/

.option-message {
  @apply hidden group-hover:block flex flex-shrink-0 focus:outline-none mx-2 block rounded-full text-gray-500 hover:text-gray-900 hover:bg-gray-700 bg-gray-800 w-8 h-8 p-2;
}

/* Live Chat Widget Styles - Ensure no interference from global styles */
iframe[title="Live Chat Support"] {
  border: 0 !important;
  background: none !important;
  background-color: transparent !important;
  margin: 0 !important;
  padding: 0 !important;
  outline: none !important;
  box-sizing: border-box !important;
  display: block !important;
}

/* Ensure no wrapper elements interfere with chat widget */
iframe[title="Live Chat Support"]:before,
iframe[title="Live Chat Support"]:after {
  display: none !important;
  content: none !important;
}
