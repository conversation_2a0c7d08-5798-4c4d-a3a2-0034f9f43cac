<template>
  <div class="bg-white px-2 rounded ">
    <div v-if="campaign?.length > 0">
      <div class="font-bold text-primary my-1">Sự kiện</div>
      <div class="flex flex-wrap md:max-h-[10svh] overflow-y-auto">
        <span
          v-for="item in campaign"
          :key="item?.campaignId"
          @click="selectedCampaign(item)"
          class="bg-slate-200 p-1 rounded px-1 mb-2 mr-3 cursor-pointer"
          >{{ item.campaignActionName }}</span
        >
      </div>
    </div>
  </div>
  <CampaignMessage
    v-if="isOpenPopup"
    @confirm="handleToogle"
    @cancel="handleToogle"
    :campaignInfo="campaignInfo"
  ></CampaignMessage>
</template>
<script setup lang="ts">
const CampaignMessage = defineAsyncComponent(
  () => import("~/components/dialog/CampaignMessage.vue")
);
const orderStore = useOrderStore();
const campaign = computed(() => orderStore.campaign);
const isOpenPopup = ref(false);
const campaignInfo = ref();
const selectedCampaign = async (item: any) => {
  campaignInfo.value = item;
  isOpenPopup.value = true;
};
const handleToogle = () => {
  isOpenPopup.value = !isOpenPopup.value;
};
</script>
