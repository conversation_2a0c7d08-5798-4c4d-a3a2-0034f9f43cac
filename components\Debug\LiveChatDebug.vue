<template>
  <div class="fixed top-4 left-4 bg-white p-4 border rounded shadow-lg z-50 text-sm">
    <h3 class="font-bold mb-2">Live Chat Debug</h3>
    <div class="space-y-1">
      <div>Plugin Available: {{ pluginAvailable ? '✅' : '❌' }}</div>
      <div>Is Loaded: {{ isLoaded ? '✅' : '❌' }}</div>
      <div>Is Visible: {{ isVisible ? '✅' : '❌' }}</div>
      <div>Script Loaded: {{ scriptLoaded ? '✅' : '❌' }}</div>
      <div>Window.LongvanChat: {{ windowChatAvailable ? '✅' : '❌' }}</div>
    </div>
    <div class="mt-2 space-x-2">
      <button @click="testShow" class="px-2 py-1 bg-blue-500 text-white rounded text-xs">Show</button>
      <button @click="testHide" class="px-2 py-1 bg-red-500 text-white rounded text-xs">Hide</button>
      <button @click="testToggle" class="px-2 py-1 bg-green-500 text-white rounded text-xs">Toggle</button>
    </div>
  </div>
</template>

<script setup lang="ts">
const { $liveChat } = useNuxtApp()

const pluginAvailable = computed(() => !!$liveChat)
const isLoaded = computed(() => $liveChat?.isLoaded?.value || false)
const isVisible = computed(() => $liveChat?.isVisible?.value || false)
const scriptLoaded = computed(() => {
  if (process.client) {
    return !!document.querySelector('script[src*="livechat.dev.longvan.vn"]')
  }
  return false
})
const windowChatAvailable = computed(() => {
  if (process.client) {
    return !!window.LongvanChat
  }
  return false
})

const testShow = () => {
  console.log('Testing show...')
  if ($liveChat) {
    $liveChat.show()
  } else {
    console.warn('$liveChat not available')
  }
}

const testHide = () => {
  console.log('Testing hide...')
  if ($liveChat) {
    $liveChat.hide()
  } else {
    console.warn('$liveChat not available')
  }
}

const testToggle = () => {
  console.log('Testing toggle...')
  if ($liveChat) {
    $liveChat.toggle()
  } else {
    console.warn('$liveChat not available')
  }
}

// Log debug info
onMounted(() => {
  console.log('LiveChat Debug mounted')
  console.log('$liveChat:', $liveChat)
  console.log('window.LongvanChat:', window.LongvanChat)
})
</script>
