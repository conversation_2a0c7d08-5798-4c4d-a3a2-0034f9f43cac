<!-- components/LiveChat.vue -->
<template>
  <div id="livechat-root"></div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";

onMounted(() => {
  // tránh tạo script nhiều lần nếu component remount
  if (!document.getElementById("livechat-script")) {
    const script = document.createElement("script");
    script.id = "livechat-script";
    script.src = "https://livechat.dev.longvan.vn/embed.js";
    script.async = true;
    document.getElementById("livechat-root")!.appendChild(script);
  }
});
</script>
