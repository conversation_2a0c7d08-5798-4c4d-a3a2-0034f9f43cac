<template>
  <div class="flex">
    <div class="w-[50px] h-[50px] mr-[10px] relative">
      <NuxtImg
        :src="handleGetImageProductUrl()"
        alt="product"
        class="object-contain w-full h-full rounded-md"
        loading="lazy"
        preload
      />
    </div>
    <div class="w-[80%] text-black text-sm space-y-1">
      <div class="font-semibold">
        {{
          `${product.title} (#${product?.id}${
            product?.sku ? ` - SKU: ${product?.sku}` : ``
          })`
        }}
      </div>
      <div class="flex items-center">
        <div class="font-semibold text-red-500">
          {{
            product.price !== null && product.price !== undefined
              ? formatCurrency(product.price)
              : "Chưa có giá"
          }} 
        </div>
        <div class="text-[12px] text-gray-300 line-through ml-2">
          {{
            product.compareAtPrice ? formatCurrency(product.compareAtPrice) : ""
          }}
        </div>
      </div>
      <!-- VAT display -->
      <div class="flex items-center text-xs">
        <span class="text-primary">VAT: </span>
        <span class="ml-1">{{ product.vat || 0 }}%</span>
        <span class="ml-2 text-primary">Giá sau VAT: </span>
        <span class="ml-1 font-semibold">{{ formatCurrency(priceAfterVAT) }}</span>
      </div>
      <div class="text-xs">
        <span v-if="promotion">{{ promotion.campaignName }}</span>
        <span v-if="promotion">
          {{
            ` (${formatTimestampV2(promotion.fromDate)}-${formatTimestampV2(
              promotion.toDate
            )})`
          }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["product"]);

const { getPromotionProductPrice } = useCampaign();
const promotion = ref<{
  campaignName: string;
  fromDate: string;
  toDate: string;
} | null>(null);

// Calculate price after VAT
const priceAfterVAT = computed(() => {
  if (props.product.price === null || props.product.price === undefined) {
    return 0;
  }
  
  const vatRate = props.product.vat || 0;
  const vatAmount = (props.product.price * vatRate) / 100;
  return props.product.price + vatAmount;
});

const handlePromotionProductPrice = async () => {
  if (props.product?.compareAtPrice) {
    const response = await getPromotionProductPrice(
      props.product.id,
      props.product.price
    );
    promotion.value = response;
  }
};
const { getImageProducrUrl } = usePortal();
const handleGetImageProductUrl = () => {
  const url = getImageProducrUrl(props.product.id, "PRODUCT");
  return url;
};
onMounted(async () => {
  await handlePromotionProductPrice();
});
</script>
