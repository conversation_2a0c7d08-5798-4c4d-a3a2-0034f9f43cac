<template>
  <div class="w-full m-auto p-2 bg-white rounded">
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
    <div>
      <div class="flex flex-row items-center justify-between mb-2">
        <span class="text-md font-bold text-primary">Khách hàng</span>
        <span
          class="cursor-pointer text-primary-light"
          @click="handleCreateCustommer"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"
            />
          </svg>
        </span>
      </div>
      <div
        v-if="users.length === 0 && isAlert && searchPhone"
        class="text-xs text-red-600 text-shadow mb-2"
      >
        Không có thông tin khách hàng vui lòng tạo mới
      </div>
      <div class="relative">
        <div class="grid grid-cols-2 gap-2">
          <div class="relative col-span-2 mb-2">
            <input
              class="w-full py-1 px-2 text-base rounded outline-none bg-secondary pr-8"
              type="text"
              placeholder="Số điện thoại"
              v-model="searchPhone"
              @input="debounceSearch('phone')"
            />
            <div
              v-if="isLoadingPhone && searchPhone"
              class="absolute top-2 right-2"
            >
              <div class="loader"></div>
            </div>
            <div
              v-if="isLoadingPhone == false && searchPhone"
              class="absolute top-2 right-2"
              @click="handleClearSearch"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                class="size-5"
              >
                <path
                  d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                />
              </svg>
            </div>
          </div>
        </div>

        <div
          v-if="
            (searchQuery || searchPhone || searchEmail) &&
            isDivVisible &&
            users.length !== 0
          "
          class="absolute z-10 w-full capitalize bg-white rounded-lg shadow-lg border border-gray-200 max-h-64 overflow-y-auto"
        >
          <div
            v-if="users.length !== 0"
            v-for="user in users"
            :key="user.id"
            @click="handleAddCustomer(user)"
            class="p-3 flex items-center cursor-pointer hover:bg-gray-100 transition-colors duration-200"
          >
            <div
              class="w-10 h-10 rounded-full bg-secondary flex items-center justify-center overflow-hidden mr-3"
            >
              <div
                class="text-white font-bold text-lg bg-blue-500 w-full h-full flex items-center justify-center"
              >
                {{ user.name.charAt(0) }}
              </div>
            </div>
            <div>
              <span class="block text-sm font-medium text-gray-800">{{
                user.name
              }}</span>
              <span class="block text-xs text-gray-500">{{ user.phone }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ConfirmDialog
      v-if="isEditOrder"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh khách hàng`"
      @confirm="confirm"
      @cancel="cancel"
    ></ConfirmDialog>
    <ModalCreateCustomer
      v-if="isModalCreateCustomer"
      @close="toggleModalCreateCustomer"
      @closeModelUser="closeModelUser"
      @dataCreateCustomer="dataCreateCustomer"
      :phoneCusTomer="searchPhone"
    ></ModalCreateCustomer>
  </div>
</template>

<script setup lang="ts">
// Lazy load heavy components
const LoadingSpinner = defineAsyncComponent(
  () => import("~/components/common/LoadingSpinner.vue")
);
const ConfirmDialog = defineAsyncComponent(
  () => import("~/components/dialog/ConfirmDialog.vue")
);
const ModalCreateCustomer = defineAsyncComponent(
  () => import("~/components/Modal/ModalCreateCustomer.vue")
);
const searchQuery = ref<string>("");
const searchPhone = ref<string>("");
const searchEmail = ref<string>("");
const users = ref<any[]>([]);
const isDivVisible = ref<boolean>(true);
const timeoutId = ref<ReturnType<typeof setTimeout> | null>(null);
const { fetchListCustomer } = useCustomer();
const isLoadingName = ref<boolean>(false);
const isLoadingPhone = ref<boolean>(false);
const cache = ref<{ [key: string]: any }>({});
const isAlert = ref<boolean>(false);
const debounceSearch = (type: string) => {
  if (timeoutId.value) clearTimeout(timeoutId.value);
  timeoutId.value = setTimeout(async () => {
    await searchCustomers(type);
    isAlert.value = true;
  }, 500);
};
const id = ref<string>("");
const { addCustomerToOrder } = useOrderStore();
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const searchCustomers = async (type: string) => {
  const searchTerm = type === "name" ? searchQuery.value : searchPhone.value;
  if (type === "name") {
    if (!searchQuery.value) {
      isLoadingName.value = false;
      return;
    }
    isLoadingName.value = true;
  } else if (type === "phone") {
    if (!searchPhone.value) {
      isLoadingPhone.value = false;
      return;
    }
    isLoadingPhone.value = true;
  }

  if (cache.value[searchTerm]) {
    users.value = cache.value[searchTerm];
    isDivVisible.value = true;
    if (type === "name") {
      isLoadingName.value = false;
    } else if (type === "phone") {
      isLoadingPhone.value = false;
    }
    return;
  }

  const request = {
    keyword: searchTerm,
    currentPage: 1,
    pageSize: 20,
  };
  const response = await fetchListCustomer(request);
  users.value = response.content;
  cache.value[searchTerm] = response.content;
  isDivVisible.value = true;

  if (type === "name") {
    isLoadingName.value = false;
  } else if (type === "phone") {
    isLoadingPhone.value = false;
  }
};

const isModalCreateCustomer = ref<boolean>(false);
const toggleModalCreateCustomer = () => {
  isModalCreateCustomer.value = !isModalCreateCustomer.value;
};

const handleCreateCustommer = () => {
  isModalCreateCustomer.value = !isModalCreateCustomer.value;
};
const handleClearSearch = () => {
  searchPhone.value = "";
};
const closeModelUser = (value: boolean) => {
  isModalCreateCustomer.value = value;
};
const dataCreateCustomer = (response: any) => {
  addCustomerToOrder(response, "");
};
const isLoading = ref<Boolean>(false);
//
const isEditOrder = ref<Boolean>(false);
const userDraft = ref();
const handleAddCustomer = async (user: any) => {
  if (orderDetail.value?.status === "APPROVED") {
    isEditOrder.value = true;
    userDraft.value = user;
  } else {
    isLoading.value = true;
    await addCustomerToOrder(user, "");
    isLoading.value = false;
  }
};
const cancel = () => {
  isEditOrder.value = false;
  users.value = [];
  searchPhone.value = "";
};
const confirm = async () => {
  isLoading.value = true;
  await addCustomerToOrder(userDraft.value, "");
  isLoading.value = false;
  userDraft.value = null;
};
</script>

<style scoped>
.loader {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top: 3px solid #3f51b5;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  margin-top: 5px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
