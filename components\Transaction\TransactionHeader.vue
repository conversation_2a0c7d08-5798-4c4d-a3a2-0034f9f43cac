<template>
  <div class="bg-white border-b border-gray-200 px-4 py-3">
    <!-- Mobile Layout -->
    <div class="md:hidden">
      <div class="flex items-center justify-between mb-3">
        <h1 class="text-lg font-semibold text-gray-900">Qu<PERSON>n lý thu chi</h1>
        <button
          @click="$emit('add-transaction')"
          class="flex items-center gap-2 px-3 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          Thêm
        </button>
      </div>

      <!-- Mobile Stats -->
      <div class="grid grid-cols-3 gap-2 text-xs">
        <div class="text-center">
          <div class="text-gray-500">Thu</div>
          <div class="font-semibold text-green-600">
            {{ formatCurrency(totalIncome) }}
          </div>
        </div>
        <div class="text-center">
          <div class="text-gray-500">Chi</div>
          <div class="font-semibold text-red-600">
            {{ formatCurrency(totalExpense) }}
          </div>
        </div>
        <div class="text-center">
          <div class="text-gray-500">Lãi</div>
          <div
            class="font-semibold"
            :class="profit >= 0 ? 'text-green-600' : 'text-red-600'"
          >
            {{ formatCurrency(profit) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Desktop Layout -->
    <div class="hidden md:flex items-center justify-between">
      <!-- Left: Title and Stats -->
      <div class="flex items-center gap-6">
        <h1 class="text-xl font-semibold text-gray-900">Quản lý thu chi</h1>
        <div class="flex items-center gap-4">
          <div class="text-sm">
            <span class="text-gray-500">Tổng thu:</span>
            <span class="font-semibold text-green-600 ml-1">{{
              formatCurrency(totalIncome)
            }}</span>
          </div>
          <div class="text-sm">
            <span class="text-gray-500">Tổng chi:</span>
            <span class="font-semibold text-red-600 ml-1">{{
              formatCurrency(totalExpense)
            }}</span>
          </div>
          <div class="text-sm">
            <span class="text-gray-500">Lợi nhuận:</span>
            <span
              class="font-semibold ml-1"
              :class="profit >= 0 ? 'text-green-600' : 'text-red-600'"
            >
              {{ formatCurrency(profit) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Right: Actions -->
      <div class="flex items-center gap-3">
        <!-- Date Range Picker -->
        <div class="flex items-center gap-2">
          <input
            type="date"
            v-model="dateRange.start"
            class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
          />
          <span class="text-gray-400">-</span>
          <input
            type="date"
            v-model="dateRange.end"
            class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>

        <!-- Export Button -->
        <button
          @click="$emit('export')"
          class="flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Xuất file
        </button>

        <!-- Add Transaction Button -->
        <button
          @click="$emit('add-transaction')"
          class="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          Thêm giao dịch
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  totalIncome: {
    type: Number,
    default: 0,
  },
  totalExpense: {
    type: Number,
    default: 0,
  },
  dateRange: {
    type: Object,
    default: () => ({
      start: "",
      end: "",
    }),
  },
});

const emit = defineEmits(["export", "add-transaction", "date-change"]);

const profit = computed(() => props.totalIncome - props.totalExpense);

watch(
  () => props.dateRange,
  (newRange) => {
    emit("date-change", newRange);
  },
  { deep: true }
);
</script>
