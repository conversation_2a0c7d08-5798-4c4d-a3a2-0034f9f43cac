<template>
  <div class="bg-white rounded-lg border border-gray-200 p-4 mb-3">
    <div class="flex items-start justify-between">
      <!-- Left: Transaction Info -->
      <div class="flex-1">
        <!-- Type and Amount -->
        <div class="flex items-center gap-3 mb-2">
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            :class="transaction.type === 'income' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'"
          >
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path v-if="transaction.type === 'income'" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              <path v-else d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
            </svg>
            {{ transaction.type === 'income' ? 'Thu' : 'Chi' }}
          </span>
          <span
            class="text-lg font-bold"
            :class="transaction.type === 'income' ? 'text-green-600' : 'text-red-600'"
          >
            {{ transaction.type === 'income' ? '+' : '-' }}{{ formatCurrency(transaction.amount) }}
          </span>
        </div>

        <!-- Category and Description -->
        <div class="mb-2">
          <div class="font-medium text-gray-900 text-sm">
            {{ transaction.category?.name }}
          </div>
          <div class="text-sm text-gray-500 mt-1" v-if="transaction.description">
            {{ transaction.description }}
          </div>
        </div>

        <!-- Date and Payment Method -->
        <div class="flex items-center justify-between text-xs text-gray-500">
          <div class="flex items-center gap-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {{ formatDate(transaction.date) }}
          </div>
          <div class="flex items-center gap-1">
            <div class="w-4 h-4 bg-gray-100 rounded flex items-center justify-center">
              <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path v-if="transaction.paymentMethod === 'cash'" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" />
                <path v-else-if="transaction.paymentMethod === 'bank'" d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
                <path v-else d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z" />
              </svg>
            </div>
            <span class="text-xs">{{ getPaymentMethodName(transaction.paymentMethod) }}</span>
          </div>
        </div>
      </div>

      <!-- Right: Actions -->
      <div class="flex items-center gap-1 ml-3">
        <button
          @click="$emit('edit', transaction)"
          class="p-2 text-gray-400 hover:text-primary rounded-lg transition-colors"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </button>
        <button
          @click="$emit('delete', transaction)"
          class="p-2 text-gray-400 hover:text-red-600 rounded-lg transition-colors"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  transaction: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['edit', 'delete'])

const getPaymentMethodName = (method) => {
  const methods = {
    cash: 'Tiền mặt',
    bank: 'Chuyển khoản',
    card: 'Thẻ'
  }
  return methods[method] || method
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
}
</script>
