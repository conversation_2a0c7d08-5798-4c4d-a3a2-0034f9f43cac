<template>
  <div class="grid grid-cols-1 md:grid-cols-4 gap-2 py-4">
    <!-- Total Income -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Tổng thu</p>
          <p class="text-2xl font-bold text-green-600">{{ formatCurrency(stats.totalIncome) }}</p>
          <p class="text-xs text-gray-500 mt-1">
            <span :class="stats.incomeChange >= 0 ? 'text-green-600' : 'text-red-600'">
              {{ stats.incomeChange >= 0 ? '+' : '' }}{{ stats.incomeChange.toFixed(1) }}%
            </span>
            so với tháng trước
          </p>
        </div>
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
        </div>
      </div>
    </div>

    <!-- Total Expense -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Tổng chi</p>
          <p class="text-2xl font-bold text-red-600">{{ formatCurrency(stats.totalExpense) }}</p>
          <p class="text-xs text-gray-500 mt-1">
            <span :class="stats.expenseChange >= 0 ? 'text-red-600' : 'text-green-600'">
              {{ stats.expenseChange >= 0 ? '+' : '' }}{{ stats.expenseChange.toFixed(1) }}%
            </span>
            so với tháng trước
          </p>
        </div>
        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
        </div>
      </div>
    </div>

    <!-- Net Profit -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Lợi nhuận</p>
          <p class="text-2xl font-bold" :class="stats.netProfit >= 0 ? 'text-green-600' : 'text-red-600'">
            {{ formatCurrency(stats.netProfit) }}
          </p>
          <p class="text-xs text-gray-500 mt-1">
            <span :class="stats.profitChange >= 0 ? 'text-green-600' : 'text-red-600'">
              {{ stats.profitChange >= 0 ? '+' : '' }}{{ stats.profitChange.toFixed(1) }}%
            </span>
            so với tháng trước
          </p>
        </div>
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
    </div>

    <!-- Transaction Count -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Số giao dịch</p>
          <p class="text-2xl font-bold text-gray-900">{{ stats.transactionCount }}</p>
          <p class="text-xs text-gray-500 mt-1">
            <span :class="stats.countChange >= 0 ? 'text-green-600' : 'text-red-600'">
              {{ stats.countChange >= 0 ? '+' : '' }}{{ stats.countChange.toFixed(1) }}%
            </span>
            so với tháng trước
          </p>
        </div>
        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  stats: {
    type: Object,
    default: () => ({
      totalIncome: 0,
      totalExpense: 0,
      netProfit: 0,
      transactionCount: 0,
      incomeChange: 0,
      expenseChange: 0,
      profitChange: 0,
      countChange: 0
    })
  }
})
</script>
