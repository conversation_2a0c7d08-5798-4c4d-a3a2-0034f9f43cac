<template>
  <div class="bg-white">
    <!-- Table Header -->
    <div class="border-b border-gray-200">
      <div
        class="grid grid-cols-12 gap-4 px-4 py-3 text-sm font-medium text-gray-700"
      >
        <div class="col-span-1">
          <input
            type="checkbox"
            v-model="selectAll"
            @change="toggleSelectAll"
            class="rounded border-gray-300 text-primary focus:ring-primary"
          />
        </div>
        <div class="col-span-2">Ngày</div>
        <div class="col-span-2">Loại</div>
        <div class="col-span-2"><PERSON>h mục</div>
        <div class="col-span-2">Số tiền</div>
        <div class="col-span-2">Phương thức</div>
        <div class="col-span-1">Thao tác</div>
      </div>
    </div>

    <!-- Table Body -->
    <div class="divide-y divide-gray-200">
      <div
        v-for="transaction in transactions"
        :key="transaction.id"
        class="grid grid-cols-12 gap-4 px-4 py-3 hover:bg-gray-50 transition-colors"
      >
        <!-- Checkbox -->
        <div class="col-span-1 flex items-center">
          <input
            type="checkbox"
            v-model="selectedItems"
            :value="transaction.id"
            class="rounded border-gray-300 text-primary focus:ring-primary"
          />
        </div>

        <!-- Date -->
        <div class="col-span-2 flex items-center">
          <div>
            <div class="text-sm font-medium text-gray-900">
              {{ formatDate(transaction.date) }}
            </div>
            <div class="text-xs text-gray-500">
              {{ formatTime(transaction.date) }}
            </div>
          </div>
        </div>

        <!-- Type -->
        <div class="col-span-2 flex items-center">
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            :class="
              transaction.type === 'income'
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            "
          >
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path
                v-if="transaction.type === 'income'"
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
              />
              <path
                v-else
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
              />
            </svg>
            {{ transaction.type === "income" ? "Thu" : "Chi" }}
          </span>
        </div>

        <!-- Category -->
        <div class="col-span-2 flex items-center">
          <div>
            <div class="text-sm font-medium text-gray-900">
              {{ transaction.category?.name }}
            </div>
            <div
              class="text-xs text-gray-500 truncate"
              v-if="transaction.description"
            >
              {{ transaction.description }}
            </div>
          </div>
        </div>

        <!-- Amount -->
        <div class="col-span-2 flex items-center">
          <span
            class="text-sm font-semibold"
            :class="
              transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
            "
          >
            {{ transaction.type === "income" ? "+" : "-"
            }}{{ formatCurrency(transaction.amount) }}
          </span>
        </div>

        <!-- Payment Method -->
        <div class="col-span-2 flex items-center">
          <div class="flex items-center gap-2">
            <div
              class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-4 h-4 text-gray-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  v-if="transaction.paymentMethod === 'cash'"
                  d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                />
                <path
                  v-else-if="transaction.paymentMethod === 'bank'"
                  d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
                />
                <path
                  v-else
                  d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z"
                />
              </svg>
            </div>
            <span class="text-sm text-gray-900">
              {{ getPaymentMethodName(transaction.paymentMethod) }}
            </span>
          </div>
        </div>

        <!-- Actions -->
        <div class="col-span-1 flex items-center">
          <div class="flex items-center gap-1">
            <button
              @click="$emit('edit', transaction)"
              class="p-1 text-gray-400 hover:text-primary rounded transition-colors"
              v-tippy="'Chỉnh sửa'"
            >
              <svg
                class="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
            </button>
            <button
              @click="$emit('delete', transaction)"
              class="p-1 text-gray-400 hover:text-red-600 rounded transition-colors"
              v-tippy="'Xóa'"
            >
              <svg
                class="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!transactions.length" class="text-center py-12">
      <svg
        class="w-12 h-12 text-gray-400 mx-auto mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
        />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        Chưa có giao dịch nào
      </h3>
      <p class="text-gray-500">
        Bắt đầu bằng cách thêm giao dịch đầu tiên của bạn
      </p>
    </div>

    <!-- Pagination -->
    <div v-if="transactions.length" class="border-t border-gray-200 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Hiển thị <span class="font-medium">{{ startItem }}</span> đến
          <span class="font-medium">{{ endItem }}</span> trong tổng số
          <span class="font-medium">{{ totalItems }}</span> giao dịch
        </div>
        <div class="flex items-center gap-2">
          <button
            @click="$emit('page-change', currentPage - 1)"
            :disabled="currentPage <= 1"
            class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Trước
          </button>
          <span class="px-3 py-2 text-sm">
            Trang {{ currentPage }} / {{ totalPages }}
          </span>
          <button
            @click="$emit('page-change', currentPage + 1)"
            :disabled="currentPage >= totalPages"
            class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Sau
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  transactions: {
    type: Array,
    default: () => [],
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  totalPages: {
    type: Number,
    default: 1,
  },
  totalItems: {
    type: Number,
    default: 0,
  },
  itemsPerPage: {
    type: Number,
    default: 20,
  },
});

const emit = defineEmits(["edit", "delete", "select-change", "page-change"]);

const selectAll = ref(false);
const selectedItems = ref([]);

const startItem = computed(
  () => (props.currentPage - 1) * props.itemsPerPage + 1
);
const endItem = computed(() =>
  Math.min(props.currentPage * props.itemsPerPage, props.totalItems)
);

const toggleSelectAll = () => {
  if (selectAll.value) {
    selectedItems.value = props.transactions.map((t) => t.id);
  } else {
    selectedItems.value = [];
  }
  emit("select-change", selectedItems.value);
};

watch(
  selectedItems,
  (newVal) => {
    emit("select-change", newVal);
  },
  { deep: true }
);

const getPaymentMethodName = (method) => {
  const methods = {
    cash: "Tiền mặt",
    bank: "Chuyển khoản",
    card: "Thẻ",
  };
  return methods[method] || method;
};

const formatDate = (date) => {
  return new Date(date).toLocaleDateString("vi-VN");
};

const formatTime = (date) => {
  return new Date(date).toLocaleTimeString("vi-VN", {
    hour: "2-digit",
    minute: "2-digit",
  });
};
</script>
