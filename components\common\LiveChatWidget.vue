<template>
  <div class="live-chat-widget">
    <!-- Chat Toggle <PERSON> (Optional - for manual control) -->
    <Transition name="fade">
      <button
        v-if="showToggleButton"
        @click="toggleChat"
        class="fixed bottom-6 right-6 z-[9998] bg-primary hover:bg-primary/90 text-white rounded-full p-4 shadow-lg transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-primary/30"
        :class="{ 'mb-16': isChatVisible }"
        aria-label="Toggle Live Chat"
      >
        <Transition name="icon-rotate" mode="out-in">
          <Icon
            v-if="isChatVisible"
            name="heroicons:x-mark-20-solid"
            class="w-6 h-6"
          />
          <Icon
            v-else
            name="heroicons:chat-bubble-left-right-20-solid"
            class="w-6 h-6"
          />
        </Transition>
      </button>
    </Transition>

    <!-- Chat Status Indicator -->
    <div
      v-if="showStatusIndicator && !isChatVisible"
      class="fixed bottom-24 right-6 z-[9997] bg-green-500 text-white text-xs px-3 py-1 rounded-full shadow-lg animate-pulse"
    >
      Hỗ trợ trực tuyến
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  showToggleButton?: boolean;
  showStatusIndicator?: boolean;
  autoShow?: boolean;
  delayShow?: number;
}

const props = withDefaults(defineProps<Props>(), {
  showToggleButton: false,
  showStatusIndicator: true,
  autoShow: true,
  delayShow: 3000,
});

// Use the live chat composable
const {
  isVisible: isChatVisible,
  isInitialized,
  isAvailable,
  init: initializeChat,
  show: showChat,
  hide: hideChat,
  toggle: toggleChat,
} = useLiveChat();

// Auto-show chat after delay if enabled
onMounted(() => {
  // Initialize chat
  initializeChat();

  // Auto-show logic
  if (props.autoShow) {
    setTimeout(() => {
      showChat();
    }, props.delayShow);
  }
});

// Expose methods for parent components
defineExpose({
  toggleChat,
  showChat,
  hideChat,
  isChatVisible: readonly(isChatVisible),
});
</script>

<style scoped>
/* Fade transition for toggle button */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.8) translateY(10px);
}

/* Icon rotation transition */
.icon-rotate-enter-active,
.icon-rotate-leave-active {
  transition: all 0.2s ease;
}

.icon-rotate-enter-from {
  opacity: 0;
  transform: rotate(-90deg) scale(0.8);
}

.icon-rotate-leave-to {
  opacity: 0;
  transform: rotate(90deg) scale(0.8);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .live-chat-widget button {
    bottom: 1rem;
    right: 1rem;
    padding: 0.75rem;
  }

  .live-chat-widget button .w-6 {
    width: 1.25rem;
    height: 1.25rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .live-chat-widget button {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .fade-enter-active,
  .fade-leave-active,
  .icon-rotate-enter-active,
  .icon-rotate-leave-active {
    transition: none;
  }

  .live-chat-widget button {
    transition: none;
  }

  .animate-pulse {
    animation: none;
  }
}
</style>
