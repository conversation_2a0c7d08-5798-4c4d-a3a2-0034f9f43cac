<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <div class="flex items-center justify-between">
        <div></div>
        <div class="font-bold">Thông tin chiến dịch</div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <!-- <PERSON><PERSON>n nội dung -->
      <div class="space-x-2">
        <span class="font-semibold">{{ campaignInfo.campaignActionName }}</span>
        <span>
          {{
            `(${formatTimestampV2(
              campaignInfo.fromDate
            )}- ${formatTimestampV2(campaignInfo.toDate)})`
          }}
        </span>
      </div>
      <!-- <div class="max-h-[200px] overflow-y-auto">
        <div class="font-semibold">Danh sách các hoạt động</div>
        <div v-for="campaign in campaignInfo.campaignActions">
          <div>
            <div>- {{ campaign?.campaignName }}</div>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const props = defineProps(["campaignInfo"]);
</script>

<style scoped></style>
