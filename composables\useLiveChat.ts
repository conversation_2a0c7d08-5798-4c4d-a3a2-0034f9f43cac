import type { LiveChatWidget } from "~/types/livechat";

/**
 * Composable for managing live chat widget functionality
 *
 * @returns Object with live chat methods and state
 */
export const useLiveChat = () => {
  const { $liveChat } = useNuxtApp();

  // Reactive state
  const isVisible = ref(false);
  const isInitialized = ref(false);

  /**
   * Initialize the live chat widget
   */
  const init = (): void => {
    if ($liveChat && !isInitialized.value) {
      $liveChat.init();
      isInitialized.value = true;
      updateVisibility();
    }
  };

  /**
   * Show the live chat widget
   */
  const show = (): void => {
    if ($liveChat) {
      $liveChat.show();
      updateVisibility();
    }
  };

  /**
   * Hide the live chat widget
   */
  const hide = (): void => {
    if ($liveChat) {
      $liveChat.hide();
      updateVisibility();
    }
  };

  /**
   * Toggle the live chat widget visibility
   */
  const toggle = (): void => {
    if ($liveChat) {
      $liveChat.toggle();
      updateVisibility();
    }
  };

  /**
   * Update the visibility state
   */
  const updateVisibility = (): void => {
    if ($liveChat) {
      isVisible.value = $liveChat.isVisible();
    }
  };

  /**
   * Check if live chat is available
   */
  const isAvailable = computed((): boolean => {
    return !!$liveChat;
  });

  // Auto-update visibility state
  onMounted(() => {
    if (process.client) {
      // Initial check
      updateVisibility();

      // Periodic check for visibility changes
      const interval = setInterval(updateVisibility, 1000);

      onUnmounted(() => {
        clearInterval(interval);
      });
    }
  });

  return {
    // State
    isVisible: readonly(isVisible),
    isInitialized: readonly(isInitialized),
    isAvailable,

    // Methods
    init,
    show,
    hide,
    toggle,
    updateVisibility,
  };
};
