# Tab Isolation Implementation Guide

## Overview

This document describes the implementation of tab-isolated context management for `orgId` and `storeId` in the Nuxt.js application. The solution prevents cross-tab interference while maintaining existing functionality.

## Problem Statement

Previously, `orgId` and `storeId` were stored in browser cookies, which are shared across all tabs in the same domain. This caused critical issues:

- Opening multiple tabs with different organization/store contexts would interfere with each other
- Users experienced data inconsistency and confusion
- Context would unexpectedly change when switching between tabs

## Solution Architecture

### Core Components

1. **`useTabContext()` Composable** - Main tab-isolated context management
2. **SessionStorage-based Storage** - Tab-isolated storage mechanism
3. **URL Parameter Sync** - Backup and synchronization mechanism
4. **Plugin Integration** - Early initialization and SDK integration

### Storage Strategy

```typescript
// Tab-isolated storage hierarchy:
1. SessionStorage (primary) - Isolated per tab
2. URL Parameters (backup) - For navigation and refresh
3. Cookies (compatibility) - For SSR and legacy support
```

## Implementation Details

### 1. Tab Context Composable (`composables/useTabContext.ts`)

```typescript
const { 
  orgId,           // Reactive org ID for current tab
  storeId,         // Reactive store ID for current tab
  tabId,           // Unique identifier for current tab
  setOrgId,        // Set org ID for current tab
  setStoreId,      // Set store ID for current tab
  setContext,      // Set both org and store ID
  clearContext,    // Clear tab context
  isOrgValid,      // Check if org ID is valid
  isStoreValid,    // Check if store ID is valid
  isContextValid   // Check if both IDs are valid
} = useTabContext();
```

### 2. Plugin Integration (`plugins/tab-context.client.ts`)

- Initializes tab context early in the application lifecycle
- Handles browser events (navigation, visibility changes)
- Ensures context is available before other components need it

### 3. SDK Integration (`plugins/sdk.ts`)

```typescript
// Before (cookie-based):
const orgId = useCookie("orgId");
const storeId = useCookie("storeId");

// After (tab-isolated):
const { orgId, storeId } = useTabContext();
```

## Migration Guide

### For New Components

Use `useTabContext()` directly:

```typescript
<script setup>
const { orgId, storeId, setOrgId, setStoreId } = useTabContext();

// Use orgId.value and storeId.value as reactive values
watch(orgId, (newOrgId) => {
  console.log('Org changed:', newOrgId);
});
</script>
```

### For Existing Components

#### Option 1: Direct Migration (Recommended)

```typescript
// Before:
const orgId = useCookie("orgId");
const storeId = useCookie("storeId");

// After:
const { orgId, storeId } = useTabContext();
```

#### Option 2: Legacy Compatibility Helper

```typescript
// For gradual migration:
const { orgId, storeId } = useLegacyContext();
// Provides cookie-like interface with tab isolation
```

### Middleware Updates

```typescript
// Before:
const orgId = useCookie("orgId");
const storeId = useCookie("storeId");

// After:
const { orgId, storeId, isOrgValid, isStoreValid } = useTabContext();
```

## Key Features

### 1. Tab Isolation

- Each browser tab maintains independent `orgId` and `storeId`
- No cross-tab interference
- Context preserved across page refreshes and navigation

### 2. URL Synchronization

- Context automatically synced with URL parameters
- Enables bookmarking and direct links with context
- Supports browser back/forward navigation

### 3. Backward Compatibility

- Cookies still maintained for SSR compatibility
- Legacy components can use migration helpers
- Gradual migration path available

### 4. Edge Case Handling

#### New Tab from Existing Tab
- Inherits context from URL parameters
- Generates new unique tab ID
- Independent context from parent tab

#### Page Refresh
- Context restored from sessionStorage
- Falls back to URL parameters if needed
- Maintains tab isolation

#### Navigation Within Tab
- Context preserved across all navigation
- URL parameters updated automatically
- Consistent experience

#### Tab Close/Reopen
- Context cleared when tab closes
- New context created when reopening
- No interference with other tabs

## Testing

### Manual Testing

1. Open `/test-tab-isolation` page
2. Open multiple tabs with different contexts
3. Verify independence between tabs
4. Test refresh and navigation scenarios

### Automated Testing

```typescript
// Example test case:
describe('Tab Isolation', () => {
  it('should maintain independent context per tab', () => {
    // Test implementation
  });
});
```

## Performance Considerations

### Benefits

- **Reduced Memory Usage**: SessionStorage is more efficient than cookies
- **Better Caching**: Smaller, more focused storage per tab
- **Faster Access**: Direct sessionStorage access vs cookie parsing

### Trade-offs

- **Slightly Larger Bundle**: Additional composable code
- **More Complex Logic**: Tab ID generation and management

## Security Considerations

- SessionStorage is tab-isolated and secure
- No cross-tab data leakage
- URL parameters are visible but expected for this use case
- Cookies maintained for SSR compatibility only

## Troubleshooting

### Common Issues

1. **Context Not Persisting**
   - Check if sessionStorage is available
   - Verify tab ID generation
   - Check URL parameter sync

2. **Cross-Tab Interference**
   - Verify sessionStorage usage (not localStorage)
   - Check tab ID uniqueness
   - Ensure proper plugin initialization

3. **SSR Issues**
   - Verify cookie fallback for server-side
   - Check client-side hydration
   - Ensure proper plugin order

### Debug Tools

```typescript
// Debug current tab context:
const { logContext, exportContext } = useTabContextDebug();
logContext(); // Console output
const data = exportContext(); // Export for analysis
```

## Future Enhancements

1. **Context Sharing**: Optional context sharing between related tabs
2. **Persistence Options**: Optional localStorage persistence for specific use cases
3. **Context History**: Track context changes for debugging
4. **Advanced Sync**: Real-time sync with server-side context

## Conclusion

The tab isolation implementation successfully resolves cross-tab interference issues while maintaining backward compatibility and existing functionality. The solution is scalable, performant, and provides a solid foundation for future enhancements.
