<template>
  <div class="flex h-screen">
    <div class="flex flex-col flex-1 overflow-y-auto scroll-smooth">
      <HeaderNonStore></HeaderNonStore>
      <div class="flex-1 min-h-full">
        <slot></slot>
      </div>
    </div>

    <!-- Live Chat Widget -->
    <LiveChatWidget />
  </div>
</template>

<script setup>
// Components
const LiveChatWidget = defineAsyncComponent(() =>
  import("~/components/common/LiveChatWidget.vue")
);
</script>

<style scoped>
/* Optimize scroll performance for 120Hz displays */
.scroll-smooth {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Hardware acceleration */
.flex-1 {
  will-change: scroll-position;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}
</style>
