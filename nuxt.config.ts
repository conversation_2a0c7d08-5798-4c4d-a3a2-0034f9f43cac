import pkg from "./package.json";
import { createResolver } from "@nuxt/kit";

const { resolve } = createResolver(import.meta.url);
export default defineNuxtConfig({
  //enable ssr
  ssr: false,

  app: {
    head: {
      titleTemplate: `%s | ${process.env.SITE_TITLE ?? "POS System"}`,
      htmlAttrs: { lang: "vi" },
      meta: [
        { charset: "utf-8" },
        { name: "viewport", content: "width=device-width, initial-scale=1" },
        { name: "description", content: "<PERSON>ệ thống POS tối ưu cho bán hàng" },
      ],
      link: [{ rel: "icon", href: "/favicon.png", type: "image/svg+xml" }],
    },
    // pageTransition: { name: "fade", mode: "out-in", appear: true },
    pageTransition: false,
  },

  components: [{ path: resolve("./components"), pathPrefix: false }],

  modules: [
    "@nuxtjs/tailwindcss",
    "nuxt-icon",
    "@nuxt/image",
    "@vueuse/nuxt",
    "@pinia/nuxt",
    "@pinia-plugin-persistedstate/nuxt",
    "nuxt-swiper",
    "@vite-pwa/nuxt",
  ],
  pwa: {
    mode: process.env.NODE_ENV === "production" ? "production" : "development",
    strategies: "generateSW",
    registerType: "autoUpdate",
    manifest: {
      name: "DMS - Distribution Management System",
      short_name: "DMS",
      description: "Hệ thống quản lý phân phối tối ưu",
      theme_color: "#0D47A1",
      background_color: "#ffffff",
      display: "standalone",
      orientation: "portrait",
      icons: [
        {
          src: "/images/1024.png",
          sizes: "64x64",
          type: "image/png",
        },
        {
          src: "/images/1024.png",
          sizes: "144x144",
          type: "image/png",
        },
        {
          src: "/images/1024.png",
          sizes: "512x512",
          type: "image/png",
        },
      ],
    },
    workbox: {
      globPatterns: ["**/*.{js,css,html,png,svg,ico,webp,avif}"],
      maximumFileSizeToCacheInBytes: 5 * 1024 * 1024, // 5MB
      runtimeCaching: [
        {
          urlPattern: /^https:\/\/api\./,
          handler: "NetworkFirst",
          options: {
            cacheName: "api-cache",
            expiration: {
              maxEntries: 100,
              maxAgeSeconds: 60 * 60 * 24, // 24 hours
            },
          },
        },
        {
          urlPattern: /\.(?:png|jpg|jpeg|svg|webp|avif)$/,
          handler: "CacheFirst",
          options: {
            cacheName: "images-cache",
            expiration: {
              maxEntries: 200,
              maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
            },
          },
        },
      ],
    },
    client: {
      installPrompt: true,
      periodicSyncForUpdates: 20,
    },
    devOptions: {
      enabled: process.env.NODE_ENV === "development",
      suppressWarnings: true,
      navigateFallback: "/",
      navigateFallbackAllowlist: [/^\/$/],
    },
  },

  // devServer: {
  //   host: "*************",
  // },

  runtimeConfig: {
    public: {
      version: pkg.version || "0.0.0",
      LOGO: "/images/logo-lvs.png",
      ENV_NAME: process.env.ENV_NAME,
    },
  },

  css: ["/assets/css/main.css"],
  plugins: [
    { src: "~/plugins/sdk.ts" }, // chạy cả server + client
    { src: "~/plugins/toastify.ts", mode: "client" },
    { src: "~/plugins/tippy.ts", mode: "client" },
  ],
  devtools: { enabled: true },
  vite: {
    define: {
      global: "window",
    },
    build: {
      minify: "esbuild",
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["vue", "pinia"],
            ui: ["@heroicons/vue", "tippy.js"],
            charts: [
              "chart.js",
              "vue-chartjs",
              "chartjs-plugin-datalabels",
              "chartjs-plugin-doughnutlabel-rebourne",
            ],
            utils: ["date-fns", "date-fns-tz", "xlsx"],
            forms: [
              "flatpickr",
              "vue-flatpickr-component",
              "vue-currency-input",
            ],
          },
        },
      },
    },
    optimizeDeps: {
      include: [
        "vue",
        "pinia",
        "@heroicons/vue",
        "chart.js",
        "vue-chartjs",
        "date-fns",
        "xlsx",
      ],
    },
  },
  // Build optimization
  nitro: {
    compressPublicAssets: true,
    minify: true,
    experimental: {
      wasm: true,
    },
    prerender: {
      crawlLinks: false,
      routes: ["/"],
    },
    storage: {
      redis: {
        driver: "redis",
        // Redis configuration for caching
      },
    },
  },
  image: {
    format: ["webp", "avif"],
    quality: 80,
    screens: {
      xs: 320,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536,
    },
    presets: {
      avatar: {
        modifiers: {
          format: "webp",
          width: 50,
          height: 50,
          quality: 80,
        },
      },
      thumbnail: {
        modifiers: {
          format: "webp",
          width: 200,
          height: 200,
          quality: 75,
        },
      },
    },
  },
});
