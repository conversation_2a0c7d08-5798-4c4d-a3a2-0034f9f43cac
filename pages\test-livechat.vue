<template>
  <div class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-center mb-8 text-primary">
        Live Chat Integration Test
      </h1>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Debug Information -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span>Plugin Available:</span>
              <span
                :class="pluginAvailable ? 'text-green-600' : 'text-red-600'"
              >
                {{ pluginAvailable ? "✅ Yes" : "❌ No" }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>Is Initialized:</span>
              <span :class="isInitialized ? 'text-green-600' : 'text-red-600'">
                {{ isInitialized ? "✅ Yes" : "❌ No" }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>Is Visible:</span>
              <span :class="isVisible ? 'text-green-600' : 'text-red-600'">
                {{ isVisible ? "✅ Yes" : "❌ No" }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>Iframe Created:</span>
              <span :class="iframeCreated ? 'text-green-600' : 'text-red-600'">
                {{ iframeCreated ? "✅ Yes" : "❌ No" }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>Widget URL:</span>
              <span class="text-blue-600 text-xs">
                {{ widgetUrl }}
              </span>
            </div>
          </div>
        </div>

        <!-- Control Panel -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-xl font-semibold mb-4">Control Panel</h2>
          <div class="space-y-4">
            <button
              @click="testInit"
              class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Initialize Chat
            </button>
            <button
              @click="testShow"
              class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              Show Chat
            </button>
            <button
              @click="testHide"
              class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              Hide Chat
            </button>
            <button
              @click="testToggle"
              class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            >
              Toggle Chat
            </button>
          </div>
        </div>
      </div>

      <!-- Configuration Test -->
      <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Configuration Test</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium mb-2">Primary Color:</label>
            <input
              v-model="testConfig.primaryColor"
              type="color"
              class="w-full h-10 rounded border"
            />
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">Position:</label>
            <select
              v-model="testConfig.position"
              class="w-full p-2 border rounded"
            >
              <option value="bottom-right">Bottom Right</option>
              <option value="bottom-left">Bottom Left</option>
              <option value="top-right">Top Right</option>
              <option value="top-left">Top Left</option>
            </select>
          </div>
          <div class="flex items-center">
            <input
              v-model="testConfig.autoShow"
              type="checkbox"
              id="autoShow"
              class="mr-2"
            />
            <label for="autoShow" class="text-sm">Auto Show</label>
          </div>
          <div class="flex items-center">
            <input
              v-model="testConfig.showToggleButton"
              type="checkbox"
              id="showToggleButton"
              class="mr-2"
            />
            <label for="showToggleButton" class="text-sm"
              >Show Toggle Button</label
            >
          </div>
        </div>
        <button
          @click="testInitWithConfig"
          class="mt-4 px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition-colors"
        >
          Initialize with Custom Config
        </button>
      </div>

      <!-- Logs -->
      <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Console Logs</h2>
        <div
          class="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-64 overflow-y-auto"
        >
          <div v-for="(log, index) in logs" :key="index" class="mb-1">
            {{ log }}
          </div>
        </div>
        <button
          @click="clearLogs"
          class="mt-2 px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
        >
          Clear Logs
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { LiveChatConfig } from "~/types/livechat";

// Set layout
definePageMeta({
  layout: "dashboard",
});

// Use live chat composable
const {
  isVisible,
  isInitialized,
  isAvailable,
  init: initializeChat,
  show: showChat,
  hide: hideChat,
  toggle: toggleChat,
} = useLiveChat();

const { $liveChat } = useNuxtApp();

// Reactive state
const logs = ref<string[]>([]);
const testConfig = ref<LiveChatConfig>({
  primaryColor: "#0D47A1",
  position: "bottom-right",
  autoShow: false,
  showToggleButton: true,
  zIndex: 10000,
});

// Computed properties for debug info
const pluginAvailable = computed(() => !!$liveChat);
const iframeCreated = computed(() => {
  if (process.client) {
    return !!document.querySelector('iframe[src*="livechat.dev.longvan.vn"]');
  }
  return false;
});
const widgetUrl = computed(() => {
  if (process.client) {
    const iframe = document.querySelector(
      'iframe[src*="livechat.dev.longvan.vn"]'
    ) as HTMLIFrameElement;
    return iframe?.src || "Not loaded";
  }
  return "Not available";
});

// Helper function to add logs
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.push(`[${timestamp}] ${message}`);
  console.log(message);
};

// Test functions
const testInit = async () => {
  addLog("Testing initialization...");
  try {
    await initializeChat();
    addLog("✅ Initialization successful");
  } catch (error) {
    addLog(`❌ Initialization failed: ${error}`);
  }
};

const testShow = () => {
  addLog("Testing show...");
  try {
    showChat();
    addLog("✅ Show command executed");
  } catch (error) {
    addLog(`❌ Show failed: ${error}`);
  }
};

const testHide = () => {
  addLog("Testing hide...");
  try {
    hideChat();
    addLog("✅ Hide command executed");
  } catch (error) {
    addLog(`❌ Hide failed: ${error}`);
  }
};

const testToggle = () => {
  addLog("Testing toggle...");
  try {
    toggleChat();
    addLog("✅ Toggle command executed");
  } catch (error) {
    addLog(`❌ Toggle failed: ${error}`);
  }
};

const testInitWithConfig = async () => {
  addLog(
    `Testing initialization with config: ${JSON.stringify(testConfig.value)}`
  );
  try {
    await initializeChat(testConfig.value);
    addLog("✅ Custom configuration initialization successful");
  } catch (error) {
    addLog(`❌ Custom configuration initialization failed: ${error}`);
  }
};

const clearLogs = () => {
  logs.value = [];
};

// Auto-log debug info on mount
onMounted(() => {
  addLog("🚀 Live Chat Test Page loaded");
  addLog(`Plugin available: ${pluginAvailable.value}`);
  addLog(`Iframe created: ${iframeCreated.value}`);
  addLog(`Widget URL: ${widgetUrl.value}`);
});
</script>

<style scoped>
/* Custom scrollbar for logs */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #374151;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
