<template>
  <div class="mx-2">
    <!-- Header -->
    <TransactionHeader
      :total-income="stats.totalIncome"
      :total-expense="stats.totalExpense"
      :date-range="dateRange"
      @export="handleExport"
      @add-transaction="showTransactionForm = true"
      @date-change="handleDateChange"
    />

    <!-- Stats (Desktop only) -->
    <div class="hidden md:block">
      <TransactionStats :stats="stats" />
    </div>

    <!-- Filters -->
    <TransactionFilters
      :categories="categories"
      @filter-change="handleFilterChange"
    />

    <!-- Action Bar (Desktop only) -->
    <div class="hidden md:block bg-white border-b border-gray-200 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <button
            v-if="selectedTransactions.length > 0"
            @click="handleBulkDelete"
            class="flex items-center gap-2 px-3 py-2 text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
            Xóa {{ selectedTransactions.length }} mục
          </button>
        </div>

        <div class="flex items-center gap-3">
          <button
            @click="showCategoryManager = true"
            class="flex items-center gap-2 px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 11H5m14-4H3m16 8H7m10 4H5"
              />
            </svg>
            Quản lý danh mục
          </button>
        </div>
      </div>
    </div>

    <!-- Desktop Table -->
    <div class="hidden md:block">
      <TransactionTable
        :transactions="paginatedTransactions"
        :current-page="pagination.currentPage"
        :total-pages="pagination.totalPages"
        :total-items="pagination.totalItems"
        :items-per-page="pagination.itemsPerPage"
        @edit="handleEditTransaction"
        @delete="handleDeleteTransaction"
        @select-change="handleSelectionChange"
        @page-change="handlePageChange"
      />
    </div>

    <!-- Mobile Cards -->
    <div class="md:hidden">
      <div class="p-4 pb-20">
        <div v-if="paginatedTransactions.length > 0" class="space-y-3">
          <TransactionMobileCard
            v-for="transaction in paginatedTransactions"
            :key="transaction.id"
            :transaction="transaction"
            @edit="handleEditTransaction"
            @delete="handleDeleteTransaction"
          />
        </div>

        <!-- Mobile Empty State -->
        <div v-else class="text-center py-12">
          <svg
            class="w-12 h-12 text-gray-400 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            Chưa có giao dịch nào
          </h3>
          <p class="text-gray-500 mb-4">
            Bắt đầu bằng cách thêm giao dịch đầu tiên của bạn
          </p>
          <button
            @click="showTransactionForm = true"
            class="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 4v16m8-8H4"
              />
            </svg>
            Thêm giao dịch
          </button>
        </div>

        <!-- Mobile Pagination -->
        <div
          v-if="paginatedTransactions.length > 0"
          class="mt-6 flex items-center justify-center gap-2"
        >
          <button
            @click="handlePageChange(pagination.currentPage - 1)"
            :disabled="pagination.currentPage <= 1"
            class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
          >
            Trước
          </button>
          <span class="px-3 py-2 text-sm text-gray-700">
            {{ pagination.currentPage }} / {{ pagination.totalPages }}
          </span>
          <button
            @click="handlePageChange(pagination.currentPage + 1)"
            :disabled="pagination.currentPage >= pagination.totalPages"
            class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
          >
            Sau
          </button>
        </div>
      </div>
    </div>

    <!-- Transaction Form Modal -->
    <TransactionForm
      v-if="showTransactionForm"
      :is-edit="isEditMode"
      :transaction="editingTransaction || undefined"
      :categories="categories"
      @close="closeTransactionForm"
      @submit="handleTransactionSubmit"
    />

    <!-- Category Manager Modal -->
    <CategoryManager
      v-if="showCategoryManager"
      :categories="categories"
      @close="showCategoryManager = false"
      @save="handleCategorySave"
      @delete="handleCategoryDelete"
    />

    <!-- Loading Overlay -->
    <div
      v-if="loading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6">
        <div class="flex items-center gap-3">
          <div
            class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
          ></div>
          <span class="text-gray-700">Đang xử lý...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO
useHead({
  title: "Quản lý thu chi",
  meta: [{ name: "description", content: "Quản lý thu chi doanh nghiệp" }],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: ["ORG_ADMIN", "SALE", "SALES", "SALE_ADMIN", "SALE_MANAGER"],
  name: "Quản lý thu chi",
});

// State
const loading = ref(false);
const showTransactionForm = ref(false);
const showCategoryManager = ref(false);
const isEditMode = ref(false);
const editingTransaction = ref<Transaction | null>(null);
const selectedTransactions = ref<number[]>([]);

// Types
interface Transaction {
  id: number;
  type: "income" | "expense";
  amount: number;
  category?: { id: number; name: string };
  description?: string;
  paymentMethod: string;
  date: string;
  status: string;
}

interface Category {
  id: number;
  name: string;
  type: "income" | "expense";
  description?: string;
  icon?: string;
}

// Data
const transactions = ref<Transaction[]>([]);
const categories = ref<Category[]>([]);
const filteredTransactions = ref<Transaction[]>([]);

// Filters
const filters = reactive({
  type: "",
  category: "",
  paymentMethod: "",
  status: "",
  search: "",
});

const dateRange = reactive({
  start: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
    .toISOString()
    .split("T")[0],
  end: new Date().toISOString().split("T")[0],
});

// Pagination
const pagination = reactive({
  currentPage: 1,
  itemsPerPage: 20,
  totalItems: 0,
  totalPages: 1,
});

// Computed
const stats = computed(() => {
  const income = filteredTransactions.value
    .filter((t: Transaction) => t.type === "income")
    .reduce((sum: number, t: Transaction) => sum + t.amount, 0);

  const expense = filteredTransactions.value
    .filter((t: Transaction) => t.type === "expense")
    .reduce((sum: number, t: Transaction) => sum + t.amount, 0);

  return {
    totalIncome: income,
    totalExpense: expense,
    netProfit: income - expense,
    transactionCount: filteredTransactions.value.length,
    incomeChange: 12.5, // Mock data
    expenseChange: -8.3, // Mock data
    profitChange: 15.2, // Mock data
    countChange: 5.7, // Mock data
  };
});

const paginatedTransactions = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.itemsPerPage;
  const end = start + pagination.itemsPerPage;
  return filteredTransactions.value.slice(start, end);
});

// Methods
const loadTransactions = async () => {
  loading.value = true;
  try {
    // Mock API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Mock data
    transactions.value = [
      {
        id: 1,
        type: "income",
        amount: 5000000,
        category: { id: 1, name: "Bán hàng" },
        description: "Bán hàng ngày 15/12",
        paymentMethod: "cash",
        date: new Date().toISOString(),
        status: "completed",
      },
      {
        id: 2,
        type: "expense",
        amount: 2000000,
        category: { id: 2, name: "Nhập hàng" },
        description: "Nhập hàng từ nhà cung cấp",
        paymentMethod: "bank",
        date: new Date(Date.now() - ********).toISOString(),
        status: "completed",
      },
    ];

    applyFilters();
  } catch (error) {
    console.error("Error loading transactions:", error);
  } finally {
    loading.value = false;
  }
};

const loadCategories = async () => {
  try {
    // Mock data
    categories.value = [
      {
        id: 1,
        name: "Bán hàng",
        type: "income",
        description: "Doanh thu từ bán hàng",
        icon: "",
      },
      {
        id: 2,
        name: "Nhập hàng",
        type: "expense",
        description: "Chi phí nhập hàng",
        icon: "",
      },
      {
        id: 3,
        name: "Lương nhân viên",
        type: "expense",
        description: "Chi phí lương",
        icon: "",
      },
    ];
  } catch (error) {
    console.error("Error loading categories:", error);
  }
};

const applyFilters = () => {
  let filtered = [...transactions.value];

  // Apply filters
  if (filters.type) {
    filtered = filtered.filter((t) => t.type === filters.type);
  }
  if (filters.category) {
    filtered = filtered.filter(
      (t) => t.category?.id === parseInt(filters.category)
    );
  }
  if (filters.paymentMethod) {
    filtered = filtered.filter(
      (t) => t.paymentMethod === filters.paymentMethod
    );
  }
  if (filters.status) {
    filtered = filtered.filter((t) => t.status === filters.status);
  }
  if (filters.search) {
    const search = filters.search.toLowerCase();
    filtered = filtered.filter(
      (t) =>
        t.description?.toLowerCase().includes(search) ||
        t.category?.name?.toLowerCase().includes(search)
    );
  }

  // Apply date range
  if (dateRange.start && dateRange.end) {
    const start = new Date(dateRange.start);
    const end = new Date(dateRange.end);
    end.setHours(23, 59, 59, 999);

    filtered = filtered.filter((t) => {
      const date = new Date(t.date);
      return date >= start && date <= end;
    });
  }

  filteredTransactions.value = filtered;

  // Update pagination
  pagination.totalItems = filtered.length;
  pagination.totalPages = Math.ceil(filtered.length / pagination.itemsPerPage);
  pagination.currentPage = 1;
};

// Event handlers
const handleFilterChange = (newFilters: any) => {
  Object.assign(filters, newFilters);
  applyFilters();
};

const handleDateChange = (newDateRange: any) => {
  Object.assign(dateRange, newDateRange);
  applyFilters();
};

const handleEditTransaction = (transaction: Transaction) => {
  editingTransaction.value = transaction;
  isEditMode.value = true;
  showTransactionForm.value = true;
};

const handleDeleteTransaction = async (transaction: Transaction) => {
  if (confirm(`Bạn có chắc chắn muốn xóa giao dịch này?`)) {
    loading.value = true;
    try {
      // Mock API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      const index = transactions.value.findIndex(
        (t) => t.id === transaction.id
      );
      if (index > -1) {
        transactions.value.splice(index, 1);
        applyFilters();
      }
    } catch (error) {
      console.error("Error deleting transaction:", error);
    } finally {
      loading.value = false;
    }
  }
};

const handleTransactionSubmit = async (formData: any) => {
  loading.value = true;
  try {
    // Mock API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    if (isEditMode.value && editingTransaction.value) {
      // Update existing transaction
      const index = transactions.value.findIndex(
        (t) => t.id === editingTransaction.value!.id
      );
      if (index > -1) {
        transactions.value[index] = {
          ...transactions.value[index],
          ...formData,
          category: categories.value.find(
            (c) => c.id === parseInt(formData.categoryId)
          ),
        };
      }
    } else {
      // Add new transaction
      const newTransaction: Transaction = {
        id: Date.now(),
        ...formData,
        category: categories.value.find(
          (c) => c.id === parseInt(formData.categoryId)
        ),
        status: "completed",
      };
      transactions.value.unshift(newTransaction);
    }

    applyFilters();
    closeTransactionForm();
  } catch (error) {
    console.error("Error saving transaction:", error);
  } finally {
    loading.value = false;
  }
};

const closeTransactionForm = () => {
  showTransactionForm.value = false;
  isEditMode.value = false;
  editingTransaction.value = null;
};

const handleSelectionChange = (selected: number[]) => {
  selectedTransactions.value = selected;
};

const handleBulkDelete = async () => {
  if (
    confirm(
      `Bạn có chắc chắn muốn xóa ${selectedTransactions.value.length} giao dịch đã chọn?`
    )
  ) {
    loading.value = true;
    try {
      // Mock API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      transactions.value = transactions.value.filter(
        (t) => !selectedTransactions.value.includes(t.id)
      );
      selectedTransactions.value = [];
      applyFilters();
    } catch (error) {
      console.error("Error deleting transactions:", error);
    } finally {
      loading.value = false;
    }
  }
};

const handlePageChange = (page: number) => {
  pagination.currentPage = page;
};

const handleCategorySave = async (categoryData: any) => {
  loading.value = true;
  try {
    // Mock API call
    await new Promise((resolve) => setTimeout(resolve, 500));

    if (categoryData.id) {
      // Update existing category
      const index = categories.value.findIndex((c) => c.id === categoryData.id);
      if (index > -1) {
        categories.value[index] = categoryData;
      }
    } else {
      // Add new category
      const newCategory: Category = {
        ...categoryData,
        id: Date.now(),
      };
      categories.value.push(newCategory);
    }
  } catch (error) {
    console.error("Error saving category:", error);
  } finally {
    loading.value = false;
  }
};

const handleCategoryDelete = async (categoryId: number) => {
  loading.value = true;
  try {
    // Mock API call
    await new Promise((resolve) => setTimeout(resolve, 500));

    const index = categories.value.findIndex((c) => c.id === categoryId);
    if (index > -1) {
      categories.value.splice(index, 1);
    }
  } catch (error) {
    console.error("Error deleting category:", error);
  } finally {
    loading.value = false;
  }
};

const handleExport = () => {
  // Mock export functionality
  console.log("Exporting transactions...");
};

// Lifecycle
onMounted(() => {
  loadCategories();
  loadTransactions();
});
</script>
