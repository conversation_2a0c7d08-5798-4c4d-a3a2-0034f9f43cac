import { defineNuxtPlugin } from "#app";

interface LiveChatWidget {
  init: () => void;
  show: () => void;
  hide: () => void;
  toggle: () => void;
  isVisible: () => boolean;
}

export default defineNuxtPlugin(() => {
  let liveChatWidget: LiveChatWidget | null = null;
  let isLoaded = false;
  let isVisible = true;

  const CHAT_WIDGET_URL = "https://livechat.dev.longvan.vn/";

  // Create iframe for the chat widget
  const createChatIframe = (): HTMLIFrameElement => {
    const iframe = document.createElement("iframe");
    iframe.src = CHAT_WIDGET_URL;

    // Set all attributes first
    iframe.setAttribute("title", "Live Chat Support");
    iframe.setAttribute("allow", "microphone; camera");
    iframe.setAttribute("frameborder", "0");
    iframe.setAttribute("scrolling", "no");
    iframe.setAttribute("seamless", "seamless");

    // Apply styles with higher specificity to override any global styles
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
      iframe.style.cssText = `
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        left: 20px !important;
        width: calc(100vw - 40px) !important;
        height: calc(100vh - 100px) !important;
        max-width: none !important;
        border: 0 !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
        z-index: 9999 !important;
        background: none !important;
        background-color: transparent !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform: translateY(0) !important;
        opacity: 1 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        outline: none !important;
        box-sizing: border-box !important;
      `;
    } else {
      iframe.style.cssText = `
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        width: 400px !important;
        height: 600px !important;
        border: 0 !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
        z-index: 9999 !important;
        background: none !important;
        background-color: transparent !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform: translateY(0) !important;
        opacity: 1 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        outline: none !important;
        box-sizing: border-box !important;
      `;
    }

    return iframe;
  };

  // Initialize the chat widget
  const initLiveChat = async (): Promise<void> => {
    if (isLoaded || typeof window === "undefined") return;

    try {
      // Create and append the iframe
      const iframe = createChatIframe();
      document.body.appendChild(iframe);

      // Handle iframe load
      iframe.onload = () => {
        console.log("Live chat widget loaded successfully");
        isLoaded = true;
      };

      iframe.onerror = () => {
        console.error("Failed to load live chat widget");
        // Remove iframe if failed to load
        if (iframe.parentNode) {
          iframe.parentNode.removeChild(iframe);
        }
      };

      // Create widget API
      liveChatWidget = {
        init: () => {
          if (!isLoaded) initLiveChat();
        },
        show: () => {
          if (iframe) {
            iframe.style.display = "block";
            iframe.style.transform = "translateY(0)";
            iframe.style.opacity = "1";
            isVisible = true;
          }
        },
        hide: () => {
          if (iframe) {
            iframe.style.transform = "translateY(100%)";
            iframe.style.opacity = "0";
            setTimeout(() => {
              iframe.style.display = "none";
            }, 300);
            isVisible = false;
          }
        },
        toggle: () => {
          if (isVisible) {
            liveChatWidget?.hide();
          } else {
            liveChatWidget?.show();
          }
        },
        isVisible: () => isVisible,
      };

      // Handle window resize for responsive behavior
      const handleResize = () => {
        if (iframe) {
          const isMobile = window.innerWidth <= 768;

          if (isMobile) {
            // Mobile styles
            iframe.style.width = "calc(100vw - 40px)";
            iframe.style.height = "calc(100vh - 100px)";
            iframe.style.left = "20px";
            iframe.style.right = "20px";
            iframe.style.maxWidth = "none";
          } else {
            // Desktop styles
            iframe.style.width = "400px";
            iframe.style.height = "600px";
            iframe.style.left = "auto";
            iframe.style.right = "20px";
            iframe.style.maxWidth = "400px";
          }
        }
      };

      window.addEventListener("resize", handleResize);

      // Cleanup function
      const cleanup = () => {
        window.removeEventListener("resize", handleResize);
        if (iframe && iframe.parentNode) {
          iframe.parentNode.removeChild(iframe);
        }
      };

      // Handle page unload
      window.addEventListener("beforeunload", cleanup);
    } catch (error) {
      console.error("Error initializing live chat widget:", error);
    }
  };

  // Auto-initialize after a short delay for better performance
  if (process.client) {
    setTimeout(() => {
      initLiveChat();
    }, 2000); // 2 second delay for better page load performance
  }

  // Provide the widget API globally
  return {
    provide: {
      liveChat: {
        init: () => liveChatWidget?.init(),
        show: () => liveChatWidget?.show(),
        hide: () => liveChatWidget?.hide(),
        toggle: () => liveChatWidget?.toggle(),
        isVisible: () => liveChatWidget?.isVisible() ?? false,
      },
    },
  };
});
