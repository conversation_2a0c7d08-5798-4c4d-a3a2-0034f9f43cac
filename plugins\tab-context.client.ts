/**
 * Tab Context Plugin
 * 
 * Initializes tab-isolated context management on client side
 * This plugin runs early to ensure tab context is available
 * before other components and composables need it.
 */

export default defineNuxtPlugin(() => {
  // Only run on client side
  if (process.client) {
    const { initializeTabContext, syncUrlWithContext } = useTabContext({
      fallbackToUrl: true,
      syncWithUrl: true,
      persistOnRefresh: true
    });

    // Initialize tab context immediately
    initializeTabContext();

    // Handle browser navigation events
    window.addEventListener('beforeunload', () => {
      // Context is automatically preserved in sessionStorage
      // No additional cleanup needed
    });

    // Handle page visibility changes (tab switching)
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        // Tab became visible, ensure context is synced
        syncUrlWithContext();
      }
    });

    // Handle storage events from other tabs (for debugging)
    window.addEventListener('storage', (event) => {
      // This only fires for localStorage changes from other tabs
      // SessionStorage is tab-isolated, so this won't interfere
      if (event.key === 'debug_tab_context') {
        console.log('Tab context debug:', event.newValue);
      }
    });
  }
});
