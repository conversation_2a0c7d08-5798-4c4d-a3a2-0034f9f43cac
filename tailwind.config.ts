import type { Config } from "tailwindcss";

export default <Partial<Config>>{
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./nuxt.config.{js,ts}",
    "./app.vue",
  ],
  theme: {
    container: {
      center: true,
      padding: "1rem",
    },
    extend: {
      colors: {
        primary: {
          50: "#e3f2fd",
          100: "#bbdefb",
          200: "#90caf9",
          300: "#64b5f6",
          400: "#42a5f5",
          500: "#2196f3",
          600: "#1e88e5",
          700: "#1976d2",
          800: "#1565c0",
          900: "#0d47a1",
        },
        secondary: "#f8fafc",
      },
      fontFamily: {
        sans: ["Inter", "system-ui", "sans-serif"],
      },
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
      },
    },
    extend: {
      colors: {
        primary: {
          light: "#0D47A1",
          DEFAULT: process.env.PRIMARY_COLOR || "#0D47A1",
          dark: "#0D47A1",
        },
        secondary: {
          light: "#f8f9fa",
          DEFAULT: process.env.SECONDARY_COLOR || "#F1F2F5",
          dark: "#eaecf0",
        },
        myChat: {
          light: "#E3F2FD",
          DEFAULT: process.env.SECONDARY_COLOR || "#E3F2FD",
          dark: "#E3F2FD",
        },
      },
      screens: {
        "2xl": "1400px",
        xs: "300px",
      },
      height: {
        "screen-50": "calc(100vh - 70px)",
        "screen-200": "calc(100vh - 170px)",
        "screen-150": "calc(100vh - 140px)",
        "screen-175": "calc(100vh - 175px)",
        "screen-40": "calc(100vh - 65px)",
        "screen-100": "calc(100vh - 122px)",
        "screen-110": "calc(100vh - 110px)",
        "screen-80": "calc(100vh - 80px)",
      },
      maxHeight: {
        "screen-50": "calc(100vh - 80px)",
        "screen-150": "calc(100vh - 270px)",
      },
      animation: {
        popup: "popupAnimation 0.2s ease-in-out forwards",
        closeModal: "closeModal 0.5s ease-in-out forwards",
        slideUp: "slideUp 0.2s ease-in-out forwards", // Thêm animation mới
      },
      keyframes: {
        popupAnimation: {
          "0%": { transform: "scale(0.5)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
        closeModal: {
          "0%": { transform: "scale(1)", opacity: "1" },
          "100%": { transform: "scale(0.5)", opacity: "0" },
        },
        slideUp: {
          "0%": { transform: "translateY(5px)" },
          "100%": { transform: "translateY(-4px)" },
        },
      },
    },
  },
  plugins: [],
};
