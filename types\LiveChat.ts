/**
 * Live Chat Widget Types
 */

export interface LiveChatWidget {
  /**
   * Initialize the live chat widget
   */
  init: () => void;

  /**
   * Show the live chat widget
   */
  show: () => void;

  /**
   * Hide the live chat widget
   */
  hide: () => void;

  /**
   * Toggle the live chat widget visibility
   */
  toggle: () => void;

  /**
   * Check if the live chat widget is currently visible
   */
  isVisible: () => boolean;
}

export interface LiveChatConfig {
  /**
   * URL of the live chat widget
   */
  widgetUrl: string;

  /**
   * Auto-initialize the widget on page load
   */
  autoInit?: boolean;

  /**
   * Delay before auto-showing the widget (in milliseconds)
   */
  autoShowDelay?: number;

  /**
   * Show the widget by default
   */
  autoShow?: boolean;

  /**
   * Custom styling options
   */
  styling?: {
    position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
    zIndex?: number;
    borderRadius?: string;
    boxShadow?: string;
  };

  /**
   * Mobile responsive options
   */
  mobile?: {
    fullScreen?: boolean;
    customWidth?: string;
    customHeight?: string;
  };
}

declare module "#app" {
  interface NuxtApp {
    $liveChat: LiveChatWidget;
  }
}

declare module "vue" {
  interface ComponentCustomProperties {
    $liveChat: LiveChatWidget;
  }
}

export {};
